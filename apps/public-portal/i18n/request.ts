import { createRequestConfig } from "@repo/i18n/request";

// Direct import of app-specific messages
async function loadPublicPortalMessages(locale: string) {
  try {
    if (locale === 'en') {
      return (await import('@repo/i18n/messages/public-portal/en.json')).default;
    } else if (locale === 'fr') {
      return (await import('@repo/i18n/messages/public-portal/fr.json')).default;
    }
    return {};
  } catch (error) {
    console.warn(`Could not load public-portal messages for locale ${locale}:`, error);
    return {};
  }
}

// Export the configured request handler
export default createRequestConfig(loadPublicPortalMessages);
