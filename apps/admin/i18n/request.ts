import { createRequestConfig } from "@repo/i18n/request";

// Direct import of app-specific messages
async function loadAdminMessages(locale: string) {
  try {
    if (locale === 'en') {
      return (await import('@repo/i18n/messages/admin/en.json')).default;
    } else if (locale === 'fr') {
      return (await import('@repo/i18n/messages/admin/fr.json')).default;
    }
    return {};
  } catch (error) {
    console.warn(`Could not load admin messages for locale ${locale}:`, error);
    return {};
  }
}

// Export the configured request handler
export default createRequestConfig(loadAdminMessages);
