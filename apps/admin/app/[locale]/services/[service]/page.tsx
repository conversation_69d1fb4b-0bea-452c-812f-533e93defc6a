'use client';

import { notFound } from 'next/navigation';
import { Link } from '@/i18n/routing';
import { ChevronRight, FileText, DollarSign, Clock, User, Download } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';


// Service data - in a real app, this would come from a CMS or database
const servicesData = {
  'first-registration': {
    titleKey: 'firstRegistration.title',
    descriptionKey: 'firstRegistration.description',
    documents: [
      {
        nameKey: 'firstRegistration.documents.formA1',
        downloadUrl: '/documents/form-a1.pdf'
      },
      {
        nameKey: 'firstRegistration.documents.formA2',
        downloadUrl: '/documents/form-a2.pdf'
      },
      {
        nameKey: 'firstRegistration.documents.form3',
        downloadUrl: '/documents/form-3.pdf'
      }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'voluntary-sale': {
    titleKey: 'voluntarySale.title',
    descriptionKey: 'voluntarySale.description',
    documents: [
      {
        nameKey: 'voluntarySale.documents.saleAgreement',
        downloadUrl: '/documents/sale-agreement.pdf'
      },
      {
        nameKey: 'voluntarySale.documents.transferCertificate',
        downloadUrl: '/documents/transfer-certificate.pdf'
      },
      {
        nameKey: 'voluntarySale.documents.identityVerification',
        downloadUrl: '/documents/identity-verification.pdf'
      }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'new-construction-permit': {
    titleKey: 'newConstructionPermit.title',
    descriptionKey: 'newConstructionPermit.description',
    documents: [
      {
        nameKey: 'newConstructionPermit.documents.application',
        downloadUrl: '/documents/construction-permit-application.pdf'
      },
      {
        nameKey: 'newConstructionPermit.documents.architecturalPlans',
        downloadUrl: '/documents/architectural-plans.pdf'
      },
      {
        nameKey: 'newConstructionPermit.documents.siteSurvey',
        downloadUrl: '/documents/site-survey.pdf'
      }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'condominium-registration': {
    titleKey: 'condominiumRegistration.title',
    descriptionKey: 'condominiumRegistration.description',
    documents: [
      {
        nameKey: 'condominiumRegistration.documents.application',
        downloadUrl: '/documents/condominium-application.pdf'
      },
      {
        nameKey: 'condominiumRegistration.documents.ownershipPlan',
        downloadUrl: '/documents/ownership-plan.pdf'
      },
      {
        nameKey: 'condominiumRegistration.documents.managementAgreement',
        downloadUrl: '/documents/management-agreement.pdf'
      }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'donation-transfer': {
    titleKey: 'donationTransfer.title',
    descriptionKey: 'donationTransfer.description',
    documents: [
      {
        nameKey: 'donationTransfer.documents.donationDeed',
        downloadUrl: '/documents/donation-deed.pdf'
      },
      {
        nameKey: 'donationTransfer.documents.acceptanceCertificate',
        downloadUrl: '/documents/acceptance-certificate.pdf'
      },
      {
        nameKey: 'donationTransfer.documents.identityDocuments',
        downloadUrl: '/documents/identity-documents.pdf'
      }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'case-reporting': {
    titleKey: 'caseReporting.title',
    descriptionKey: 'caseReporting.description',
    documents: [
      {
        nameKey: 'caseReporting.documents.complaintForm',
        downloadUrl: '/documents/complaint-form.pdf'
      },
      {
        nameKey: 'caseReporting.documents.evidenceDocuments',
        downloadUrl: '/documents/evidence-documents.pdf'
      },
      {
        nameKey: 'caseReporting.documents.witnessStatements',
        downloadUrl: '/documents/witness-statements.pdf'
      }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'sub-lease-registration': {
    titleKey: 'subLeaseRegistration.title',
    descriptionKey: 'subLeaseRegistration.description',
    documents: [
      { nameKey: 'subLeaseRegistration.documents.subLeaseAgreement', downloadUrl: '/documents/sub-lease-agreement.pdf' },
      { nameKey: 'subLeaseRegistration.documents.originalLease', downloadUrl: '/documents/original-lease.pdf' },
      { nameKey: 'subLeaseRegistration.documents.consentLetter', downloadUrl: '/documents/consent-letter.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'leasehold-to-freehold': {
    titleKey: 'leaseholdToFreehold.title',
    descriptionKey: 'leaseholdToFreehold.description',
    documents: [
      { nameKey: 'leaseholdToFreehold.documents.conversionApplication', downloadUrl: '/documents/conversion-application.pdf' },
      { nameKey: 'leaseholdToFreehold.documents.leaseDocument', downloadUrl: '/documents/lease-document.pdf' },
      { nameKey: 'leaseholdToFreehold.documents.paymentReceipt', downloadUrl: '/documents/payment-receipt.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'privatization-registration': {
    titleKey: 'privatizationRegistration.title',
    descriptionKey: 'privatizationRegistration.description',
    documents: [
      { nameKey: 'privatizationRegistration.documents.privatizationApplication', downloadUrl: '/documents/privatization-application.pdf' },
      { nameKey: 'privatizationRegistration.documents.stateApproval', downloadUrl: '/documents/state-approval.pdf' },
      { nameKey: 'privatizationRegistration.documents.valuationReport', downloadUrl: '/documents/valuation-report.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'tenure-conversion': {
    titleKey: 'tenureConversion.title',
    descriptionKey: 'tenureConversion.description',
    documents: [
      { nameKey: 'tenureConversion.documents.conversionRequest', downloadUrl: '/documents/conversion-request.pdf' },
      { nameKey: 'tenureConversion.documents.currentTitle', downloadUrl: '/documents/current-title.pdf' },
      { nameKey: 'tenureConversion.documents.justificationLetter', downloadUrl: '/documents/justification-letter.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'right-holders-confirmation': {
    titleKey: 'rightHoldersConfirmation.title',
    descriptionKey: 'rightHoldersConfirmation.description',
    documents: [
      { nameKey: 'rightHoldersConfirmation.documents.confirmationApplication', downloadUrl: '/documents/confirmation-application.pdf' },
      { nameKey: 'rightHoldersConfirmation.documents.identityDocuments', downloadUrl: '/documents/identity-documents.pdf' },
      { nameKey: 'rightHoldersConfirmation.documents.landEvidence', downloadUrl: '/documents/land-evidence.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'succession-transfer': {
    titleKey: 'successionTransfer.title',
    descriptionKey: 'successionTransfer.description',
    documents: [
      { nameKey: 'successionTransfer.documents.successionApplication', downloadUrl: '/documents/succession-application.pdf' },
      { nameKey: 'successionTransfer.documents.deathCertificate', downloadUrl: '/documents/death-certificate.pdf' },
      { nameKey: 'successionTransfer.documents.inheritanceDocument', downloadUrl: '/documents/inheritance-document.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'exchange-transfer': {
    titleKey: 'exchangeTransfer.title',
    descriptionKey: 'exchangeTransfer.description',
    documents: [
      { nameKey: 'exchangeTransfer.documents.exchangeAgreement', downloadUrl: '/documents/exchange-agreement.pdf' },
      { nameKey: 'exchangeTransfer.documents.mutualConsent', downloadUrl: '/documents/mutual-consent.pdf' },
      { nameKey: 'exchangeTransfer.documents.propertyDescriptions', downloadUrl: '/documents/property-descriptions.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'marriage-divorce-transfer': {
    titleKey: 'marriageDivorceTransfer.title',
    descriptionKey: 'marriageDivorceTransfer.description',
    documents: [
      { nameKey: 'marriageDivorceTransfer.documents.marriageCertificate', downloadUrl: '/documents/marriage-certificate.pdf' },
      { nameKey: 'marriageDivorceTransfer.documents.propertySettlement', downloadUrl: '/documents/property-settlement.pdf' },
      { nameKey: 'marriageDivorceTransfer.documents.courtOrder', downloadUrl: '/documents/court-order.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'court-decision-transfer': {
    titleKey: 'courtDecisionTransfer.title',
    descriptionKey: 'courtDecisionTransfer.description',
    documents: [
      { nameKey: 'courtDecisionTransfer.documents.courtOrder', downloadUrl: '/documents/court-order.pdf' },
      { nameKey: 'courtDecisionTransfer.documents.executionOrder', downloadUrl: '/documents/execution-order.pdf' },
      { nameKey: 'courtDecisionTransfer.documents.legalRepresentation', downloadUrl: '/documents/legal-representation.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'auction-sale': {
    titleKey: 'auctionSale.title',
    descriptionKey: 'auctionSale.description',
    documents: [
      { nameKey: 'auctionSale.documents.auctionNotice', downloadUrl: '/documents/auction-notice.pdf' },
      { nameKey: 'auctionSale.documents.biddingDocuments', downloadUrl: '/documents/bidding-documents.pdf' },
      { nameKey: 'auctionSale.documents.auctionResults', downloadUrl: '/documents/auction-results.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'expropriation-transfer': {
    titleKey: 'expropriationTransfer.title',
    descriptionKey: 'expropriationTransfer.description',
    documents: [
      { nameKey: 'expropriationTransfer.documents.expropriationNotice', downloadUrl: '/documents/expropriation-notice.pdf' },
      { nameKey: 'expropriationTransfer.documents.compensationAgreement', downloadUrl: '/documents/compensation-agreement.pdf' },
      { nameKey: 'expropriationTransfer.documents.publicPurposeDocument', downloadUrl: '/documents/public-purpose-document.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'contract-termination': {
    titleKey: 'contractTermination.title',
    descriptionKey: 'contractTermination.description',
    documents: [
      { nameKey: 'contractTermination.documents.terminationNotice', downloadUrl: '/documents/termination-notice.pdf' },
      { nameKey: 'contractTermination.documents.originalContract', downloadUrl: '/documents/original-contract.pdf' },
      { nameKey: 'contractTermination.documents.settlementAgreement', downloadUrl: '/documents/settlement-agreement.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'abandoned-property': {
    titleKey: 'abandonedProperty.title',
    descriptionKey: 'abandonedProperty.description',
    documents: [
      { nameKey: 'abandonedProperty.documents.abandonmentDeclaration', downloadUrl: '/documents/abandonment-declaration.pdf' },
      { nameKey: 'abandonedProperty.documents.searchNotice', downloadUrl: '/documents/search-notice.pdf' },
      { nameKey: 'abandonedProperty.documents.reclamationApplication', downloadUrl: '/documents/reclamation-application.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'person-changes': {
    titleKey: 'personChanges.title',
    descriptionKey: 'personChanges.description',
    documents: [
      { nameKey: 'personChanges.documents.changeApplication', downloadUrl: '/documents/change-application.pdf' },
      { nameKey: 'personChanges.documents.identityDocuments', downloadUrl: '/documents/identity-documents.pdf' },
      { nameKey: 'personChanges.documents.proofOfChange', downloadUrl: '/documents/proof-of-change.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'land-use-change': {
    titleKey: 'landUseChange.title',
    descriptionKey: 'landUseChange.description',
    documents: [
      { nameKey: 'landUseChange.documents.useChangeApplication', downloadUrl: '/documents/use-change-application.pdf' },
      { nameKey: 'landUseChange.documents.zoningApproval', downloadUrl: '/documents/zoning-approval.pdf' },
      { nameKey: 'landUseChange.documents.impactAssessment', downloadUrl: '/documents/impact-assessment.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'boundary-rectification': {
    titleKey: 'boundaryRectification.title',
    descriptionKey: 'boundaryRectification.description',
    documents: [
      { nameKey: 'boundaryRectification.documents.rectificationApplication', downloadUrl: '/documents/rectification-application.pdf' },
      { nameKey: 'boundaryRectification.documents.surveyReport', downloadUrl: '/documents/survey-report.pdf' },
      { nameKey: 'boundaryRectification.documents.neighborConsent', downloadUrl: '/documents/neighbor-consent.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'land-subdivision': {
    titleKey: 'landSubdivision.title',
    descriptionKey: 'landSubdivision.description',
    documents: [
      { nameKey: 'landSubdivision.documents.subdivisionApplication', downloadUrl: '/documents/subdivision-application.pdf' },
      { nameKey: 'landSubdivision.documents.subdivisionPlan', downloadUrl: '/documents/subdivision-plan.pdf' },
      { nameKey: 'landSubdivision.documents.infrastructurePlan', downloadUrl: '/documents/infrastructure-plan.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'parcel-merging': {
    titleKey: 'parcelMerging.title',
    descriptionKey: 'parcelMerging.description',
    documents: [
      { nameKey: 'parcelMerging.documents.mergingApplication', downloadUrl: '/documents/merging-application.pdf' },
      { nameKey: 'parcelMerging.documents.mergedSurvey', downloadUrl: '/documents/merged-survey.pdf' },
      { nameKey: 'parcelMerging.documents.titleConsolidation', downloadUrl: '/documents/title-consolidation.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'replotting': {
    titleKey: 'replotting.title',
    descriptionKey: 'replotting.description',
    documents: [
      { nameKey: 'replotting.documents.replottingApplication', downloadUrl: '/documents/replotting-application.pdf' },
      { nameKey: 'replotting.documents.replottingPlan', downloadUrl: '/documents/replotting-plan.pdf' },
      { nameKey: 'replotting.documents.communityConsent', downloadUrl: '/documents/community-consent.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'temporal-requisition': {
    titleKey: 'temporalRequisition.title',
    descriptionKey: 'temporalRequisition.description',
    documents: [
      { nameKey: 'temporalRequisition.documents.requisitionApplication', downloadUrl: '/documents/requisition-application.pdf' },
      { nameKey: 'temporalRequisition.documents.purposeJustification', downloadUrl: '/documents/purpose-justification.pdf' },
      { nameKey: 'temporalRequisition.documents.compensationPlan', downloadUrl: '/documents/compensation-plan.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'caveat-restriction': {
    titleKey: 'caveatRestriction.title',
    descriptionKey: 'caveatRestriction.description',
    documents: [
      { nameKey: 'caveatRestriction.documents.caveatApplication', downloadUrl: '/documents/caveat-application.pdf' },
      { nameKey: 'caveatRestriction.documents.restrictionDetails', downloadUrl: '/documents/restriction-details.pdf' },
      { nameKey: 'caveatRestriction.documents.legalBasis', downloadUrl: '/documents/legal-basis.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'surety-restriction': {
    titleKey: 'suretyRestriction.title',
    descriptionKey: 'suretyRestriction.description',
    documents: [
      { nameKey: 'suretyRestriction.documents.suretyApplication', downloadUrl: '/documents/surety-application.pdf' },
      { nameKey: 'suretyRestriction.documents.suretyAgreement', downloadUrl: '/documents/surety-agreement.pdf' },
      { nameKey: 'suretyRestriction.documents.financialGuarantee', downloadUrl: '/documents/financial-guarantee.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'servitude-restriction': {
    titleKey: 'servitudeRestriction.title',
    descriptionKey: 'servitudeRestriction.description',
    documents: [
      { nameKey: 'servitudeRestriction.documents.servitudeApplication', downloadUrl: '/documents/servitude-application.pdf' },
      { nameKey: 'servitudeRestriction.documents.servitudeAgreement', downloadUrl: '/documents/servitude-agreement.pdf' },
      { nameKey: 'servitudeRestriction.documents.beneficiaryConsent', downloadUrl: '/documents/beneficiary-consent.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'bankruptcy-restriction': {
    titleKey: 'bankruptcyRestriction.title',
    descriptionKey: 'bankruptcyRestriction.description',
    documents: [
      { nameKey: 'bankruptcyRestriction.documents.bankruptcyApplication', downloadUrl: '/documents/bankruptcy-application.pdf' },
      { nameKey: 'bankruptcyRestriction.documents.bankruptcyOrder', downloadUrl: '/documents/bankruptcy-order.pdf' },
      { nameKey: 'bankruptcyRestriction.documents.creditorInformation', downloadUrl: '/documents/creditor-information.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'collateral-lending': {
    titleKey: 'collateralLending.title',
    descriptionKey: 'collateralLending.description',
    documents: [
      { nameKey: 'collateralLending.documents.collateralApplication', downloadUrl: '/documents/collateral-application.pdf' },
      { nameKey: 'collateralLending.documents.loanAgreement', downloadUrl: '/documents/loan-agreement.pdf' },
      { nameKey: 'collateralLending.documents.propertyValuation', downloadUrl: '/documents/property-valuation.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'property-tax-registration': {
    titleKey: 'propertyTaxRegistration.title',
    descriptionKey: 'propertyTaxRegistration.description',
    documents: [
      { nameKey: 'propertyTaxRegistration.documents.taxRegistrationForm', downloadUrl: '/documents/tax-registration-form.pdf' },
      { nameKey: 'propertyTaxRegistration.documents.propertyDetails', downloadUrl: '/documents/property-details.pdf' },
      { nameKey: 'propertyTaxRegistration.documents.ownershipProof', downloadUrl: '/documents/ownership-proof.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'property-tax-declaration': {
    titleKey: 'propertyTaxDeclaration.title',
    descriptionKey: 'propertyTaxDeclaration.description',
    documents: [
      { nameKey: 'propertyTaxDeclaration.documents.taxDeclarationForm', downloadUrl: '/documents/tax-declaration-form.pdf' },
      { nameKey: 'propertyTaxDeclaration.documents.propertyValuation', downloadUrl: '/documents/property-valuation.pdf' },
      { nameKey: 'propertyTaxDeclaration.documents.incomeStatement', downloadUrl: '/documents/income-statement.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'inspection-notice': {
    titleKey: 'inspectionNotice.title',
    descriptionKey: 'inspectionNotice.description',
    documents: [
      { nameKey: 'inspectionNotice.documents.inspectionRequest', downloadUrl: '/documents/inspection-request.pdf' },
      { nameKey: 'inspectionNotice.documents.propertyAccess', downloadUrl: '/documents/property-access.pdf' },
      { nameKey: 'inspectionNotice.documents.inspectionPurpose', downloadUrl: '/documents/inspection-purpose.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'permit-renewal': {
    titleKey: 'permitRenewal.title',
    descriptionKey: 'permitRenewal.description',
    documents: [
      { nameKey: 'permitRenewal.documents.renewalApplication', downloadUrl: '/documents/renewal-application.pdf' },
      { nameKey: 'permitRenewal.documents.originalPermit', downloadUrl: '/documents/original-permit.pdf' },
      { nameKey: 'permitRenewal.documents.complianceReport', downloadUrl: '/documents/compliance-report.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'fence-construction': {
    titleKey: 'fenceConstruction.title',
    descriptionKey: 'fenceConstruction.description',
    documents: [
      { nameKey: 'fenceConstruction.documents.fenceApplication', downloadUrl: '/documents/fence-application.pdf' },
      { nameKey: 'fenceConstruction.documents.fencePlan', downloadUrl: '/documents/fence-plan.pdf' },
      { nameKey: 'fenceConstruction.documents.neighborConsent', downloadUrl: '/documents/neighbor-consent.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'demolition': {
    titleKey: 'demolition.title',
    descriptionKey: 'demolition.description',
    documents: [
      { nameKey: 'demolition.documents.demolitionApplication', downloadUrl: '/documents/demolition-application.pdf' },
      { nameKey: 'demolition.documents.demolitionPlan', downloadUrl: '/documents/demolition-plan.pdf' },
      { nameKey: 'demolition.documents.wasteDisposalPlan', downloadUrl: '/documents/waste-disposal-plan.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'occupancy-permit': {
    titleKey: 'occupancyPermit.title',
    descriptionKey: 'occupancyPermit.description',
    documents: [
      { nameKey: 'occupancyPermit.documents.occupancyApplication', downloadUrl: '/documents/occupancy-application.pdf' },
      { nameKey: 'occupancyPermit.documents.completionCertificate', downloadUrl: '/documents/completion-certificate.pdf' },
      { nameKey: 'occupancyPermit.documents.safetyInspection', downloadUrl: '/documents/safety-inspection.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'project-modification': {
    titleKey: 'projectModification.title',
    descriptionKey: 'projectModification.description',
    documents: [
      { nameKey: 'projectModification.documents.modificationApplication', downloadUrl: '/documents/modification-application.pdf' },
      { nameKey: 'projectModification.documents.originalApproval', downloadUrl: '/documents/original-approval.pdf' },
      { nameKey: 'projectModification.documents.modificationPlans', downloadUrl: '/documents/modification-plans.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'temporary-structure': {
    titleKey: 'temporaryStructure.title',
    descriptionKey: 'temporaryStructure.description',
    documents: [
      { nameKey: 'temporaryStructure.documents.temporaryApplication', downloadUrl: '/documents/temporary-application.pdf' },
      { nameKey: 'temporaryStructure.documents.structurePlan', downloadUrl: '/documents/structure-plan.pdf' },
      { nameKey: 'temporaryStructure.documents.durationJustification', downloadUrl: '/documents/duration-justification.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'building-refurbishment': {
    titleKey: 'buildingRefurbishment.title',
    descriptionKey: 'buildingRefurbishment.description',
    documents: [
      { nameKey: 'buildingRefurbishment.documents.refurbishmentApplication', downloadUrl: '/documents/refurbishment-application.pdf' },
      { nameKey: 'buildingRefurbishment.documents.refurbishmentPlan', downloadUrl: '/documents/refurbishment-plan.pdf' },
      { nameKey: 'buildingRefurbishment.documents.structuralAssessment', downloadUrl: '/documents/structural-assessment.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'building-use-change': {
    titleKey: 'buildingUseChange.title',
    descriptionKey: 'buildingUseChange.description',
    documents: [
      { nameKey: 'buildingUseChange.documents.useChangeApplication', downloadUrl: '/documents/use-change-application.pdf' },
      { nameKey: 'buildingUseChange.documents.currentUseDocument', downloadUrl: '/documents/current-use-document.pdf' },
      { nameKey: 'buildingUseChange.documents.newUseJustification', downloadUrl: '/documents/new-use-justification.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'zoning-change': {
    titleKey: 'zoningChange.title',
    descriptionKey: 'zoningChange.description',
    documents: [
      { nameKey: 'zoningChange.documents.zoningApplication', downloadUrl: '/documents/zoning-application.pdf' },
      { nameKey: 'zoningChange.documents.zoningPlan', downloadUrl: '/documents/zoning-plan.pdf' },
      { nameKey: 'zoningChange.documents.impactStudy', downloadUrl: '/documents/impact-study.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'urbanization-inspection': {
    titleKey: 'urbanizationInspection.title',
    descriptionKey: 'urbanizationInspection.description',
    documents: [
      { nameKey: 'urbanizationInspection.documents.inspectionRequest', downloadUrl: '/documents/inspection-request.pdf' },
      { nameKey: 'urbanizationInspection.documents.projectDetails', downloadUrl: '/documents/project-details.pdf' },
      { nameKey: 'urbanizationInspection.documents.inspectionScope', downloadUrl: '/documents/inspection-scope.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  },
  'urbanization-renewal': {
    titleKey: 'urbanizationRenewal.title',
    descriptionKey: 'urbanizationRenewal.description',
    documents: [
      { nameKey: 'urbanizationRenewal.documents.renewalApplication', downloadUrl: '/documents/renewal-application.pdf' },
      { nameKey: 'urbanizationRenewal.documents.originalApproval', downloadUrl: '/documents/original-approval.pdf' },
      { nameKey: 'urbanizationRenewal.documents.progressReport', downloadUrl: '/documents/progress-report.pdf' }
    ],
    details: {
      institutionKey: 'institution',
      priceKey: 'price',
      timeFrameKey: 'timeFrame',
      reviewKey: 'review'
    }
  }
};

export default function ServicePage() {
  const params = useParams();
  const service = params.service as string;
  const serviceData = servicesData[service as keyof typeof servicesData];

  if (!serviceData) {
    notFound();
  }

  const t = useTranslations('servicePage');

  // Get translated content
  const title = t(serviceData.titleKey);
  const description = t(serviceData.descriptionKey);
  
  const documents = serviceData.documents.map(doc => ({
    name: t(doc.nameKey),
    downloadUrl: doc.downloadUrl
  }));

  const details = {
    institution: t('institutionValue'),
    price: t('priceValue'),
    timeFrame: t('timeFrameValue'),
    review: t('reviewValue')
  };

  return (
    <div className="min-h-screen py-12">
      {/* Breadcrumbs */}
      <div className="">
        <div className="px-[8vw] xl:max-w-7xl xl:mx-auto xl:px-0 py-4">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-custom-light-blue hover:text-custom-light-blue/80">
                {t('breadcrumb')}
              </Link>
              <ChevronRight className="w-4 h-4 text-gray-400" />
              <span className="text-custom-gray-7">{title}</span>
            </nav>
        </div>
      </div>

      <div className="px-[8vw] xl:max-w-7xl xl:mx-auto xl:px-0 py-8">
      <div>
              <h1 className="text-3xl font-bold text-custom-blue-3 mb-6">{title}</h1>
            </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Page Title */}

            {/* Description */}
            <div className=" rounded-2xl border border-custom-gray-5 ">
              <div className="p-8">
              <h2 className="text-xl font-semibold text-custom-blue-3 mb-4">{t('description')}</h2>
              <p className="text-custom-gray-6 leading-relaxed mb-8">{description}</p>
              <h2 className="text-xl font-semibold text-custom-blue-3 mb-4 ">
                {t('applicationForm')}
              </h2>
              <div className="space-y-3">
                {documents.map((doc, index) => (
                  <div key={index} className="flex flex-col md:flex-row md:items-center justify-between p-3 ">
                    <div className="flex items-center space-x-3">
                      <div className="w-1 h-1 bg-custom-gray-6 rounded-full"></div>
                      <span className="text-custom-gray-6">{doc.name}</span>
                    </div>
                    <a
                      href={doc.downloadUrl}
                      className="flex items-center space-x-2 text-custom-gray-7 font-semibold"
                    >
                      <Download className="w-4 h-4" />
                      <span>{t('downloadCopy')}</span>
                    </a>
                  </div>
                ))}
              </div>
              </div>
              <div className="text-center border-t border-custom-gray-5 py-4 px-8">
              <button className="bg-custom-light-blue hover:bg-custom-light-blue/80 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200 w-full">
                {t('startApplication')}
              </button>
            </div>
            </div>



            {/* Start Application Button */}

          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className=" rounded-2xl border border-custom-gray-5 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-custom-blue-3 mb-6">{t('details')}</h3>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <FileText className="w-5 h-5 text-custom-light-blue mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-custom-light-blue">{t('institution')}</p>
                    <p className="text-custom-gray-7">{details.institution}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <DollarSign className="w-5 h-5 text-custom-light-blue mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-custom-light-blue">{t('price')}</p>
                    <p className="text-custom-gray-7">{details.price}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-custom-light-blue mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-custom-light-blue">{t('timeFrame')}</p>
                    <p className="text-custom-gray-7">{details.timeFrame}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <User className="w-5 h-5 text-custom-light-blue mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-custom-light-blue">{t('review')}</p>
                    <p className="text-custom-gray-7">{details.review}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Note: generateStaticParams is not needed for client components
