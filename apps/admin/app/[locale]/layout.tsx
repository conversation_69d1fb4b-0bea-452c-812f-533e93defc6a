import { NextIntlClientProvider, hasLocale } from "next-intl";
import { notFound } from "next/navigation";
import { routing } from "@/i18n/routing";
import { getMessages } from "next-intl/server";
import { Suspense } from "react";
import LoadingScreen from "@/components/common/LoadingScreen";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import type { Metadata } from 'next';

type Props = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const awaitedParams = await params;
  const locale = awaitedParams.locale;
  
  const isFrench = locale === 'fr';
  
  return {
    title: {
      default: isFrench ? 'Cadastre - Système de Gestion Foncière' : 'Cadastre - Land Management System',
      template: isFrench ? '%s | Cadastre - Système de Gestion Foncière' : '%s | Cadastre - Land Management System'
    },
    description: isFrench 
      ? 'Plateforme professionnelle de services cadastraux et de gestion foncière. Gérez les dossiers de propriété, les levés de terrain, les limites de propriété et la documentation immobilière avec des capacités de cartographie et SIG avancées.'
      : 'Professional land management and cadastral services platform. Manage property records, land surveys, property boundaries, and real estate documentation with advanced mapping and GIS capabilities.',
    keywords: isFrench 
      ? [
          'cadastre',
          'gestion foncière',
          'dossiers de propriété',
          'levé de terrain',
          'immobilier',
          'limites de propriété',
          'cartographie SIG',
          'services cadastraux',
          'administration foncière',
          'gestion de propriété',
          'arpentage',
          'registre foncier'
        ]
      : [
          'cadastre',
          'land management',
          'property records',
          'land survey',
          'real estate',
          'property boundaries',
          'GIS mapping',
          'cadastral services',
          'land administration',
          'property management',
          'surveying',
          'land registry'
        ],
    openGraph: {
      title: isFrench ? 'Cadastre - Système de Gestion Foncière' : 'Cadastre - Land Management System',
      description: isFrench 
        ? 'Plateforme professionnelle de services cadastraux et de gestion foncière. Gérez les dossiers de propriété et la documentation immobilière.'
        : 'Professional land management and cadastral services platform. Manage property records, land surveys, and real estate documentation.',
      locale: isFrench ? 'fr_FR' : 'en_US',
    },
    twitter: {
      title: isFrench ? 'Cadastre - Système de Gestion Foncière' : 'Cadastre - Land Management System',
      description: isFrench 
        ? 'Plateforme professionnelle de services cadastraux et de gestion foncière.'
        : 'Professional land management and cadastral services platform.',
    },
    alternates: {
      canonical: `/${locale}`,
      languages: {
        'fr-FR': '/fr',
        'en-US': '/en',
      },
    },
  };
}

export default async function LocaleLayout(props: Props) {
  const awaitedParams = await props.params;
  const locale = awaitedParams.locale;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const messages = await getMessages({ locale });

  return (
    <NextIntlClientProvider messages={messages} locale={locale}>
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1">
          <Suspense fallback={<LoadingScreen />}>{props.children}</Suspense>
        </main>
        <Footer />
      </div>
    </NextIntlClientProvider>
  );
}
