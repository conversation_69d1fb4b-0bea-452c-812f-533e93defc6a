{"name": "admin", "version": "0.1.0", "description": "Professional land management and cadastral services platform. Manage property records, land surveys, property boundaries, and real estate documentation with advanced mapping and GIS capabilities.", "keywords": ["cadastre", "land-management", "property-records", "land-survey", "real-estate", "property-boundaries", "gis-mapping", "cadastral-services", "land-administration", "property-management", "surveying", "land-registry"], "author": "Cadastre Team", "license": "MIT", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@repo/i18n": "workspace:*", "@repo/react-query": "workspace:*", "@changey/react-leaflet-markercluster": "^4.0.0-rc1", "@faker-js/faker": "^9.6.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.8", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.71.1", "@tanstack/react-query-devtools": "^5.72.2", "@tanstack/react-table": "^8.21.2", "@tensorflow-models/coco-ssd": "^2.2.3", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-node": "^4.22.0", "@types/react-datepicker": "^7.0.0", "@types/react-pdf": "^6.2.0", "@xyflow/react": "^12.8.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dom-to-image-more": "^3.6.3", "elkjs": "^0.10.0", "face-api.js": "^0.22.2", "file-saver": "^2.0.5", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "i18n-iso-countries": "^7.14.0", "i18n-nationality": "^1.4.0", "input-otp": "^1.4.2", "iso-3166-1-alpha-2": "^1.0.2", "js-cookie": "^3.0.5", "jspdf": "^3.0.2", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.485.0", "mindee": "^4.29.0-rc5", "next": "15.2.4", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-barcode": "^1.6.1", "react-cookie": "^8.0.1", "react-countup": "^6.5.3", "react-datepicker": "^8.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-pdf": "^9.2.1", "react-rnd": "^10.5.2", "react-select": "^5.10.2", "react-select-country-list": "^2.2.3", "react-simple-maps": "^3.0.0", "react-spinners": "^0.15.0", "react-to-print": "^3.1.1", "react-webcam": "^7.2.0", "reactflow": "^11.11.4", "recharts": "^2.15.4", "sonner": "^2.0.3", "supercluster": "^8.0.1", "swiper": "^11.2.6", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "use-debounce": "^10.0.4", "use-supercluster": "^1.2.0", "xlsx": "^0.18.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@iconify/react": "^5.2.1", "@tailwindcss/postcss": "^4", "@types/file-saver": "^2.0.7", "@types/iso-3166-1-alpha-2": "^1.0.2", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.17", "@types/node": "^20.17.29", "@types/qrcode": "^1.5.5", "@types/qrcode.react": "^1.0.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@types/react-simple-maps": "^3.0.6", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.4", "husky": "^9.1.7", "tailwindcss": "^4", "typescript": "^5"}}