'use client';

import { useState } from 'react';
import { Icon } from '@iconify/react';
import { useTranslations } from 'next-intl';

export default function HeroSection() {
  const [searchQuery, setSearchQuery] = useState('');
  const t = useTranslations('hero');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle search logic here
    console.log('Searching for:', searchQuery);
  };

  return (
    <section className='px-4 sm:px-6 lg:px-[6vw]'>
      <div className="relative py-12 sm:py-16 lg:py-20 rounded-2xl">
        {/* Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat rounded-2xl"
          style={{
            backgroundImage: 'url("/images/hero-bg.png")'
          }}
        />
        
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/30 rounded-2xl"></div>
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-white mb-6 leading-tight">
            {t('title')}
          </h1>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <form onSubmit={handleSearch} className="relative">
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-0">
                <div className="relative flex-1">
                  <Icon icon="mdi:magnify" className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8" />
                  <input
                    type="text"
                    placeholder={t('searchPlaceholder')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 sm:pl-12 lg:pl-14 pr-4 py-3 sm:py-4 border border-gray-300 rounded-lg sm:rounded-l-lg sm:rounded-r-none outline-none bg-white text-gray-900 text-sm sm:text-base"
                  />
                </div>
                <button
                  type="submit"
                  className="bg-custom-blue hover:bg-custom-blue/90 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg sm:rounded-l-none sm:rounded-r-lg font-semibold text-sm sm:text-base transition-colors duration-200"
                >
                  {t('searchButton')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}
