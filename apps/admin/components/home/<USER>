'use client';

import { useState, useRef, useEffect } from 'react';
import { Link } from '@/i18n/routing';
import { Icon } from '@iconify/react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTranslations } from 'next-intl';

interface Service {
  id: string;
  nameKey: string;
  href: string;
  categoryKey: string;
}

const services: Service[] = [
  // Land Registration
  { id: 'first-registration', nameKey: 'items.firstRegistration', href: '/services/first-registration', categoryKey: 'categories.landRegistration' },
  { id: 'condominium-registration', nameKey: 'items.condominiumRegistration', href: '/services/condominium-registration', categoryKey: 'categories.landRegistration' },
  { id: 'sub-lease-registration', nameKey: 'items.subLeaseRegistration', href: '/services/sub-lease-registration', categoryKey: 'categories.landRegistration' },
  { id: 'leasehold-to-freehold', nameKey: 'items.leaseholdToFreehold', href: '/services/leasehold-to-freehold', categoryKey: 'categories.landRegistration' },
  { id: 'privatization-registration', nameKey: 'items.privatizationRegistration', href: '/services/privatization-registration', categoryKey: 'categories.landRegistration' },
  { id: 'tenure-conversion', nameKey: 'items.tenureConversion', href: '/services/tenure-conversion', categoryKey: 'categories.landRegistration' },
  { id: 'right-holders-confirmation', nameKey: 'items.rightHoldersConfirmation', href: '/services/right-holders-confirmation', categoryKey: 'categories.landRegistration' },

  // Land Transfer
  { id: 'voluntary-sale', nameKey: 'items.voluntarySale', href: '/services/voluntary-sale', categoryKey: 'categories.landTransfer' },
  { id: 'donation-transfer', nameKey: 'items.donationTransfer', href: '/services/donation-transfer', categoryKey: 'categories.landTransfer' },
  { id: 'succession-transfer', nameKey: 'items.successionTransfer', href: '/services/succession-transfer', categoryKey: 'categories.landTransfer' },
  { id: 'exchange-transfer', nameKey: 'items.exchangeTransfer', href: '/services/exchange-transfer', categoryKey: 'categories.landTransfer' },
  { id: 'marriage-divorce-transfer', nameKey: 'items.marriageDivorceTransfer', href: '/services/marriage-divorce-transfer', categoryKey: 'categories.landTransfer' },
  { id: 'court-decision-transfer', nameKey: 'items.courtDecisionTransfer', href: '/services/court-decision-transfer', categoryKey: 'categories.landTransfer' },
  { id: 'auction-sale', nameKey: 'items.auctionSale', href: '/services/auction-sale', categoryKey: 'categories.landTransfer' },
  { id: 'expropriation-transfer', nameKey: 'items.expropriationTransfer', href: '/services/expropriation-transfer', categoryKey: 'categories.landTransfer' },
  { id: 'contract-termination', nameKey: 'items.contractTermination', href: '/services/contract-termination', categoryKey: 'categories.landTransfer' },
  { id: 'abandoned-property', nameKey: 'items.abandonedProperty', href: '/services/abandoned-property', categoryKey: 'categories.landTransfer' },

  // Land Modification
  { id: 'person-changes', nameKey: 'items.personChanges', href: '/services/person-changes', categoryKey: 'categories.landModification' },
  { id: 'land-use-change', nameKey: 'items.landUseChange', href: '/services/land-use-change', categoryKey: 'categories.landModification' },
  { id: 'boundary-rectification', nameKey: 'items.boundaryRectification', href: '/services/boundary-rectification', categoryKey: 'categories.landModification' },
  { id: 'land-subdivision', nameKey: 'items.landSubdivision', href: '/services/land-subdivision', categoryKey: 'categories.landModification' },
  { id: 'parcel-merging', nameKey: 'items.parcelMerging', href: '/services/parcel-merging', categoryKey: 'categories.landModification' },
  { id: 'replotting', nameKey: 'items.replotting', href: '/services/replotting', categoryKey: 'categories.landModification' },

  // Restriction
  { id: 'temporal-requisition', nameKey: 'items.temporalRequisition', href: '/services/temporal-requisition', categoryKey: 'categories.restriction' },
  { id: 'caveat-restriction', nameKey: 'items.caveatRestriction', href: '/services/caveat-restriction', categoryKey: 'categories.restriction' },
  { id: 'surety-restriction', nameKey: 'items.suretyRestriction', href: '/services/surety-restriction', categoryKey: 'categories.restriction' },
  { id: 'servitude-restriction', nameKey: 'items.servitudeRestriction', href: '/services/servitude-restriction', categoryKey: 'categories.restriction' },
  { id: 'bankruptcy-restriction', nameKey: 'items.bankruptcyRestriction', href: '/services/bankruptcy-restriction', categoryKey: 'categories.restriction' },
  { id: 'collateral-lending', nameKey: 'items.collateralLending', href: '/services/collateral-lending', categoryKey: 'categories.restriction' },

  // Land Tax
  { id: 'property-tax-registration', nameKey: 'items.propertyTaxRegistration', href: '/services/property-tax-registration', categoryKey: 'categories.landTax' },
  { id: 'property-tax-declaration', nameKey: 'items.propertyTaxDeclaration', href: '/services/property-tax-declaration', categoryKey: 'categories.landTax' },

  // Conflict Resolution
  { id: 'case-reporting', nameKey: 'items.caseReporting', href: '/services/case-reporting', categoryKey: 'categories.conflictResolution' },

  // Construction Permit
  { id: 'new-construction-permit', nameKey: 'items.newConstructionPermit', href: '/services/new-construction-permit', categoryKey: 'categories.constructionPermit' },
  { id: 'inspection-notice', nameKey: 'items.inspectionNotice', href: '/services/inspection-notice', categoryKey: 'categories.constructionPermit' },
  { id: 'permit-renewal', nameKey: 'items.permitRenewal', href: '/services/permit-renewal', categoryKey: 'categories.constructionPermit' },
  { id: 'fence-construction', nameKey: 'items.fenceConstruction', href: '/services/fence-construction', categoryKey: 'categories.constructionPermit' },
  { id: 'demolition', nameKey: 'items.demolition', href: '/services/demolition', categoryKey: 'categories.constructionPermit' },
  { id: 'occupancy-permit', nameKey: 'items.occupancyPermit', href: '/services/occupancy-permit', categoryKey: 'categories.constructionPermit' },
  { id: 'project-modification', nameKey: 'items.projectModification', href: '/services/project-modification', categoryKey: 'categories.constructionPermit' },
  { id: 'temporary-structure', nameKey: 'items.temporaryStructure', href: '/services/temporary-structure', categoryKey: 'categories.constructionPermit' },
  { id: 'building-refurbishment', nameKey: 'items.buildingRefurbishment', href: '/services/building-refurbishment', categoryKey: 'categories.constructionPermit' },
  { id: 'building-use-change', nameKey: 'items.buildingUseChange', href: '/services/building-use-change', categoryKey: 'categories.constructionPermit' },

  // Urbanization
  { id: 'zoning-change', nameKey: 'items.zoningChange', href: '/services/zoning-change', categoryKey: 'categories.urbanization' },
  { id: 'urbanization-inspection', nameKey: 'items.urbanizationInspection', href: '/services/urbanization-inspection', categoryKey: 'categories.urbanization' },
  { id: 'urbanization-renewal', nameKey: 'items.urbanizationRenewal', href: '/services/urbanization-renewal', categoryKey: 'categories.urbanization' },
];

export default function ServicesSection() {
  const t = useTranslations('services');
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Group services by category
  const servicesByCategory = services.reduce((acc, service) => {
    const categoryName = t(service.categoryKey);
    if (!acc[categoryName]) {
      acc[categoryName] = [];
    }
    acc[categoryName].push(service);
    return acc;
  }, {} as Record<string, Service[]>);

  const categories = Object.keys(servicesByCategory);
  const allCategories = [t('viewAll'), ...categories];

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScrollButtons);
      return () => scrollContainer.removeEventListener('scroll', checkScrollButtons);
    }
  }, [categories]);

  return (
    <section className=" bg-white">
      <div className="px-[6vw] ">
        <Tabs defaultValue={t('viewAll')} className="w-full">
          <div className="relative mb-8">
            {/* Left scroll button */}
            {canScrollLeft && (
              <button
                onClick={scrollLeft}
                className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-200 rounded-full p-2 shadow-md hover:shadow-lg transition-shadow"
                aria-label="Scroll left"
              >
                <Icon icon="mdi:chevron-left" className="w-4 h-4 text-gray-600" />
              </button>
            )}
            
            {/* Right scroll button */}
            {canScrollRight && (
              <button
                onClick={scrollRight}
                className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-200 rounded-full p-2 shadow-md hover:shadow-lg transition-shadow"
                aria-label="Scroll right"
              >
                <Icon icon="mdi:chevron-right" className="w-4 h-4 text-gray-600" />
              </button>
            )}

            <TabsList 
              ref={scrollContainerRef}
              onScroll={checkScrollButtons}
              className="flex w-full bg-white items-start border-b rounded-none p-0 h-auto overflow-x-auto scrollbar-hide whitespace-nowrap justify-start px-4"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {allCategories.map((category) => (
                <TabsTrigger 
                  key={category} 
                  value={category}
                  className="text-sm font-medium px-6 py-4  bg-white text-custom-gray-3 hover:text-gray-900 data-[state=active]:text-custom-light-blue data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-custom-light-blue rounded-none border-b-2 border-transparent transition-all duration-200 flex-shrink-0 shadow-none"
                >
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
          
          {/* View All Tab Content */}
          <TabsContent value={t('viewAll')} className="mt-0">
            <div className="space-y-8">
              {categories.map((category, index) => (
                <div key={category} className="relative">
                  <div className="flex flex-col md:flex-row ">
                    <div className="md:w-1/4 pr-6">
                      <h3 className="text-2xl font-semibold text-custom-blue-3 mb-4">{category}</h3>
                    </div>
                    <div className="md:w-3/4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {servicesByCategory[category].map((service) => (
                          <Link
                            key={service.id}
                            href={service.href}
                            className="flex items-center justify-between p-4 rounded-lg   transition-all duration-200 group"
                          >
                            <span className="text-custom-blue group-hover:text-custom-blue/80 font-medium text-sm">
                              {t(service.nameKey)}
                            </span>
                            <Icon 
                              icon="mdi:arrow-right" 
                              className="w-4 h-4 text-custom-blue group-hover:text-custom-blue/80 group-hover:translate-x-1 transition-all" 
                            />
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                  {index < categories.length - 1 && (
                    <div className="border-b border-gray-200 mt-8"></div>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>
          
          {/* Individual Category Tabs - Keep existing UI */}
          {categories.map((category) => (
            <TabsContent key={category} value={category} className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {servicesByCategory[category].map((service) => (
                  <Link
                    key={service.id}
                    href={service.href}
                    className="flex items-center justify-between p-4 rounded-lg  transition-all duration-200 group"
                  >
                    <span className="text-custom-blue group-hover:text-custom-blue/80  font-medium text-sm">
                      {t(service.nameKey)}
                    </span>
                    <Icon 
                      icon="mdi:arrow-right" 
                      className="w-4 h-4 text-custom-blue group-hover:text-custom-blue/80  group-hover:translate-x-1 transition-all" 
                    />
                  </Link>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
}
