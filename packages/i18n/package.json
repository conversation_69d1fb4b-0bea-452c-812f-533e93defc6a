{"name": "@repo/i18n", "version": "0.0.0", "private": true, "exports": {"./routing": "./src/routing.ts", "./request": "./src/request.ts", "./config": "./src/config.ts", "./utils": "./src/utils.ts", "./messages/*": "./messages/*"}, "scripts": {"lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"next-intl": "^4.1.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "eslint": "^9.34.0", "typescript": "5.9.2"}, "peerDependencies": {"next": ">=14.0.0", "react": ">=18.0.0"}}