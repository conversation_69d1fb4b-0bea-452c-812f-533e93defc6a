import { SupportedLocale } from './config';

/**
 * Utility functions for loading and managing translations
 */

/**
 * Load app-specific messages for a given app and locale
 * 
 * @param appName - Name of the app (e.g., 'public-portal', 'admin')
 * @param locale - Locale to load messages for
 * @returns Promise that resolves to the messages object
 */
export async function loadAppMessages(appName: string, locale: SupportedLocale) {
  try {
    return (await import(`../messages/${appName}/${locale}.json`)).default;
  } catch (error) {
    console.warn(`Could not load app messages for ${appName}/${locale}:`, error);
    return {};
  }
}

/**
 * Create a message loader function for a specific app
 * This is useful for creating app-specific request configurations
 * 
 * @param appName - Name of the app
 * @returns Function that loads messages for the given app
 */
export function createAppMessageLoader(appName: string) {
  return (locale: string) => loadAppMessages(appName, locale as SupportedLocale);
}

/**
 * Load messages from a custom path
 * This allows apps to load messages from their own directories
 * 
 * @param messagesPath - Path to the messages directory relative to the app root
 * @param locale - Locale to load messages for
 * @returns Promise that resolves to the messages object
 */
export async function loadCustomMessages(messagesPath: string, locale: SupportedLocale) {
  try {
    // This will be resolved relative to the calling app
    const messages = await import(`${messagesPath}/${locale}.json`);
    return messages.default;
  } catch (error) {
    console.warn(`Could not load custom messages from ${messagesPath}/${locale}:`, error);
    return {};
  }
}

/**
 * Create a message loader function for a custom path
 * 
 * @param messagesPath - Path to the messages directory
 * @returns Function that loads messages from the custom path
 */
export function createCustomMessageLoader(messagesPath: string) {
  return (locale: string) => loadCustomMessages(messagesPath, locale as SupportedLocale);
}
