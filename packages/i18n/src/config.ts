/**
 * Shared i18n configuration for the Cadastre monorepo
 * This file contains common configuration that can be used across all apps
 */

export const SUPPORTED_LOCALES = ['en', 'fr'] as const;
export type SupportedLocale = typeof SUPPORTED_LOCALES[number];

export const DEFAULT_LOCALE: SupportedLocale = 'fr';

export const LOCALE_LABELS: Record<SupportedLocale, string> = {
  en: 'English',
  fr: 'Français',
};

export const LOCALE_FLAGS: Record<SupportedLocale, string> = {
  en: '🇺🇸',
  fr: '🇫🇷',
};

/**
 * Common i18n configuration that can be extended by individual apps
 */
export const I18N_CONFIG = {
  locales: SUPPORTED_LOCALES,
  defaultLocale: DEFAULT_LOCALE,
  localeLabels: LOCALE_LABELS,
  localeFlags: LOCALE_FLAGS,
} as const;
