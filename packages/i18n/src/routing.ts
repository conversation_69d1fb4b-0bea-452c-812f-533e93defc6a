import { defineRouting } from "next-intl/routing";
import { I18N_CONFIG } from "./config";

/**
 * Shared routing configuration for next-intl
 * This can be used across all apps in the monorepo
 */
export const routing = defineRouting({
  // A list of all locales that are supported
  locales: I18N_CONFIG.locales,

  // Used when no locale matches
  defaultLocale: I18N_CONFIG.defaultLocale,
});

// Re-export createNavigation for apps to use
export { createNavigation } from "next-intl/navigation";
