import { getRequestConfig } from "next-intl/server";
import { routing } from "./routing";

/**
 * Deep merge utility for combining translation objects
 */
function deepMerge(target: any, source: any): any {
  const result = { ...target };

  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }

  return result;
}

/**
 * Load shared messages from the @repo/i18n package
 */
async function loadSharedMessages(locale: string) {
  try {
    return (await import(`../messages/${locale}.json`)).default;
  } catch (error) {
    console.warn(`Could not load shared messages for locale ${locale}:`, error);
    return {};
  }
}

/**
 * Create a request configuration that merges shared and app-specific messages
 *
 * @param getAppMessages - Function to get app-specific messages for a locale
 * @returns Request configuration function
 */
export function createRequestConfig(
  getAppMessages?: (locale: string) => Promise<any>
) {
  return getRequestConfig(async ({ requestLocale }) => {
    // This typically corresponds to the `[locale]` segment
    let locale = await requestLocale;

    // Ensure that a valid locale is used
    if (!locale || !routing.locales.includes(locale as any)) {
      locale = routing.defaultLocale;
    }

    // Load shared messages
    const sharedMessages = await loadSharedMessages(locale);

    // Load app-specific messages if provided
    let appMessages = {};
    if (getAppMessages) {
      try {
        appMessages = await getAppMessages(locale);
      } catch (error) {
        console.warn(`Could not load app messages for locale ${locale}:`, error);
      }
    }

    // Merge shared and app-specific messages (app messages take precedence)
    const messages = deepMerge(sharedMessages, appMessages);

    return {
      locale,
      messages,
    };
  });
}

/**
 * Default request configuration that only loads shared messages
 * This can be used by apps that don't need app-specific translations
 */
export default createRequestConfig();
