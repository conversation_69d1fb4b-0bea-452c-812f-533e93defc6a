{"name": "@repo/react-query", "version": "0.0.0", "private": true, "exports": {"./provider": "./src/provider.tsx", "./client": "./src/client.ts", "./hooks": "./src/hooks.ts", "./utils": "./src/utils.ts"}, "scripts": {"lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@tanstack/react-query": "^5.71.1", "@tanstack/react-query-devtools": "^5.72.2"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.34.0", "typescript": "5.9.2"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}