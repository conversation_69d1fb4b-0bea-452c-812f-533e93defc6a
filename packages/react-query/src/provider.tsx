'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { createQueryClient, defaultQueryClientConfig } from './client';

interface ReactQueryProviderProps {
  children: React.ReactNode;
  /**
   * Custom query client configuration
   * If not provided, the default configuration will be used
   */
  config?: Partial<typeof defaultQueryClientConfig>;
  /**
   * Whether to show React Query DevTools
   * Defaults to true in development, false in production
   */
  showDevtools?: boolean;
  /**
   * Position of the DevTools panel
   */
  devtoolsPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

/**
 * Shared React Query Provider for the Cadastre monorepo
 * This provider can be used across all apps with consistent configuration
 */
export function ReactQueryProvider({ 
  children, 
  config,
  showDevtools = process.env.NODE_ENV === 'development',
  devtoolsPosition = 'bottom-right'
}: ReactQueryProviderProps) {
  const [queryClient] = useState(() => createQueryClient(config));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {showDevtools && (
        <ReactQueryDevtools 
          initialIsOpen={false} 
          position={devtoolsPosition}
        />
      )}
    </QueryClientProvider>
  );
}

/**
 * Default React Query Provider with standard configuration
 * This is the most common usage and can be used directly by most apps
 */
export default ReactQueryProvider;
