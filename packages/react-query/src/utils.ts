import { QueryClient } from '@tanstack/react-query';

/**
 * Utility functions for React Query operations
 */

/**
 * Prefetch data for a given query
 * Useful for optimistic loading and improving user experience
 */
export async function prefetchQuery<TData>(
  queryClient: QueryClient,
  queryKey: readonly unknown[],
  queryFn: () => Promise<TData>,
  options?: {
    staleTime?: number;
    gcTime?: number;
  }
) {
  await queryClient.prefetchQuery({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes
    gcTime: options?.gcTime ?? 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Set query data optimistically
 * Useful for immediate UI updates before server confirmation
 */
export function setOptimisticData<TData>(
  queryClient: QueryClient,
  queryKey: readonly unknown[],
  data: TData | ((oldData: TData | undefined) => TData)
) {
  queryClient.setQueryData(queryKey, data);
}

/**
 * Remove query from cache
 * Useful for cleaning up sensitive data or forcing fresh fetches
 */
export function removeQuery(
  queryClient: QueryClient,
  queryKey: readonly unknown[]
) {
  queryClient.removeQueries({ queryKey });
}

/**
 * Cancel ongoing queries
 * Useful for preventing race conditions and cleaning up on component unmount
 */
export async function cancelQueries(
  queryClient: QueryClient,
  queryKey?: readonly unknown[]
) {
  if (queryKey) {
    await queryClient.cancelQueries({ queryKey });
  } else {
    await queryClient.cancelQueries();
  }
}

/**
 * Get cached query data
 * Useful for accessing cached data without triggering a refetch
 */
export function getCachedData<TData>(
  queryClient: QueryClient,
  queryKey: readonly unknown[]
): TData | undefined {
  return queryClient.getQueryData<TData>(queryKey);
}

/**
 * Check if a query is currently fetching
 * Useful for showing loading states
 */
export function isQueryFetching(
  queryClient: QueryClient,
  queryKey?: readonly unknown[]
): boolean {
  return queryClient.isFetching({ queryKey }) > 0;
}

/**
 * Check if a query is currently mutating
 * Useful for showing loading states for mutations
 */
export function isQueryMutating(
  queryClient: QueryClient,
  mutationKey?: readonly unknown[]
): boolean {
  return queryClient.isMutating({ mutationKey }) > 0;
}

/**
 * Reset all queries to their initial state
 * Useful for logout scenarios or when switching contexts
 */
export async function resetQueries(
  queryClient: QueryClient,
  queryKey?: readonly unknown[]
) {
  if (queryKey) {
    await queryClient.resetQueries({ queryKey });
  } else {
    await queryClient.resetQueries();
  }
}

/**
 * Clear all cached data
 * Useful for memory management or when switching users
 */
export function clearCache(queryClient: QueryClient) {
  queryClient.clear();
}

/**
 * Batch multiple query operations
 * Useful for performing multiple operations atomically
 */
export async function batchQueryOperations(
  queryClient: QueryClient,
  operations: (() => Promise<void> | void)[]
) {
  // Suspend all query updates
  queryClient.getQueryCache().clear();
  
  try {
    // Execute all operations
    await Promise.all(operations.map(op => op()));
  } finally {
    // Resume query updates
    queryClient.resumePausedMutations();
  }
}
