import { QueryClient } from '@tanstack/react-query';

/**
 * Default query client configuration for the Cadastre monorepo
 * This configuration provides sensible defaults for caching, error handling, and retries
 */
export const defaultQueryClientConfig = {
  defaultOptions: {
    queries: {
      // Disable automatic refetching on window focus to prevent unnecessary requests
      refetchOnWindowFocus: false,
      
      // Refetch every 20 minutes to keep data reasonably fresh
      refetchInterval: 60 * 1000 * 20,
      
      // Retry failed requests up to 3 times with exponential backoff
      retry: 3,
      
      // Consider data stale after 5 minutes
      staleTime: 5 * 60 * 1000,
      
      // Keep data in cache for 10 minutes after it becomes unused
      gcTime: 10 * 60 * 1000,
      
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
      
      // Retry delay for mutations
      retryDelay: 1000,
    },
  },
};

/**
 * Creates a new QueryClient with the default configuration
 * A<PERSON> can extend this configuration by passing additional options
 */
export function createQueryClient(overrides?: Partial<typeof defaultQueryClientConfig>) {
  const config = {
    ...defaultQueryClientConfig,
    ...overrides,
    defaultOptions: {
      ...defaultQueryClientConfig.defaultOptions,
      ...overrides?.defaultOptions,
      queries: {
        ...defaultQueryClientConfig.defaultOptions.queries,
        ...overrides?.defaultOptions?.queries,
      },
      mutations: {
        ...defaultQueryClientConfig.defaultOptions.mutations,
        ...overrides?.defaultOptions?.mutations,
      },
    },
  };

  return new QueryClient(config);
}

/**
 * Default query client instance
 * This can be used directly by apps that don't need custom configuration
 */
export const queryClient = createQueryClient();
