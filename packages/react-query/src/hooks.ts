import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';

/**
 * Common query keys used across the application
 * This helps maintain consistency and avoid key conflicts
 */
export const queryKeys = {
  // User-related queries
  user: ['user'] as const,
  userProfile: (id: string) => ['user', 'profile', id] as const,
  
  // Property-related queries
  properties: ['properties'] as const,
  property: (id: string) => ['properties', id] as const,
  propertyDocuments: (id: string) => ['properties', id, 'documents'] as const,
  
  // Survey-related queries
  surveys: ['surveys'] as const,
  survey: (id: string) => ['surveys', id] as const,
  
  // Application-related queries
  applications: ['applications'] as const,
  application: (id: string) => ['applications', id] as const,
  
  // Service-related queries
  services: ['services'] as const,
  service: (id: string) => ['services', id] as const,
  
  // Report-related queries
  reports: ['reports'] as const,
  report: (id: string) => ['reports', id] as const,
} as const;

/**
 * Custom hook for handling API errors consistently
 */
export function useErrorHandler() {
  return (error: unknown) => {
    console.error('API Error:', error);
    
    // You can add global error handling logic here
    // For example, showing toast notifications, logging to external services, etc.
    
    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('401')) {
        // Handle unauthorized errors
        console.warn('Unauthorized access detected');
      } else if (error.message.includes('403')) {
        // Handle forbidden errors
        console.warn('Forbidden access detected');
      } else if (error.message.includes('404')) {
        // Handle not found errors
        console.warn('Resource not found');
      } else if (error.message.includes('500')) {
        // Handle server errors
        console.error('Server error detected');
      }
    }
  };
}

/**
 * Enhanced useQuery hook with consistent error handling
 */
export function useAppQuery<TData = unknown, TError = Error>(
  options: UseQueryOptions<TData, TError>
) {
  const handleError = useErrorHandler();
  
  return useQuery({
    ...options,
    onError: (error) => {
      handleError(error);
      options.onError?.(error);
    },
  });
}

/**
 * Enhanced useMutation hook with consistent error handling and success feedback
 */
export function useAppMutation<TData = unknown, TError = Error, TVariables = void, TContext = unknown>(
  options: UseMutationOptions<TData, TError, TVariables, TContext>
) {
  const handleError = useErrorHandler();
  const queryClient = useQueryClient();
  
  return useMutation({
    ...options,
    onError: (error, variables, context) => {
      handleError(error);
      options.onError?.(error, variables, context);
    },
    onSuccess: (data, variables, context) => {
      // You can add global success handling logic here
      // For example, showing success notifications
      
      options.onSuccess?.(data, variables, context);
    },
    onSettled: (data, error, variables, context) => {
      // Invalidate and refetch queries after mutations
      // This ensures the UI stays in sync with the server state
      
      options.onSettled?.(data, error, variables, context);
    },
  });
}

/**
 * Hook for invalidating queries by pattern
 */
export function useInvalidateQueries() {
  const queryClient = useQueryClient();
  
  return {
    invalidateAll: () => queryClient.invalidateQueries(),
    invalidateUser: () => queryClient.invalidateQueries({ queryKey: queryKeys.user }),
    invalidateProperties: () => queryClient.invalidateQueries({ queryKey: queryKeys.properties }),
    invalidateSurveys: () => queryClient.invalidateQueries({ queryKey: queryKeys.surveys }),
    invalidateApplications: () => queryClient.invalidateQueries({ queryKey: queryKeys.applications }),
    invalidateServices: () => queryClient.invalidateQueries({ queryKey: queryKeys.services }),
    invalidateReports: () => queryClient.invalidateQueries({ queryKey: queryKeys.reports }),
  };
}
